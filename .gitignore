# Dependencies
node_modules/
backend/node_modules/
frontend/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Build outputs
dist/
build/
.next/
.out/

# Cache
.npm
.yarn/cache
.eslintcache
.parcel-cache

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# AI Model and Service files
# Python cache and bytecode
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Virtual environments
venv/
env/
ENV/
ai-service/venv/
ai-model/venv/

# AI Database files
medical_chroma_db/
ai-model/medical_chroma_db/
ai-service/medical_chroma_db/
*.sqlite3
*.db

# Large dataset files
*.csv
ai-model/*.csv
ai-model/processed_medical_data.json

# AI Model specific
ai-model/.env
ai-service/.env
ai-model/.idea/
ai-service/.idea/